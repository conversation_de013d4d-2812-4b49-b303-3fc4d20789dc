The `CamerasTabContent` in "lib/search/widgets/cameras_tab_content.dart" has issue.

Expected behavior:
When the scrollbar of `CamerasTabContent` TabView is scrolled-down, the `SliverAppBar` inside `NestedScrollView` in "lib/search/widgets/search_screen_active.dart" that contains `<PERSON>Field` should be hidden.

Current behavior:
- When `CamerasTabContent` TabView is still in loading state, and then I scroll-down the scrollbar, the `SliverAppBar` in `NestedScrollView` inside `SearchScreenActive` widget is hidden correctly.
- But when `CamerasTabContent` TabView data has been loaded, and then I scroll down its scrollbar, the `SliverAppBar` in `NestedScrollView` inside `SearchScreenActive` wdiget stays pinned/visible.

The cause:
- When `CamerasTabContent` tab view in loading state, the scroll is still owned by `NestedScrollView` in `SearchScreenActive` widget. So the sliver behavior works properly.
- But when `CamerasTabContent` tab view data is loaded, what I scroll becomes the `MasonryGridView` of that `CamerasTabContent` tab.

The solution:
Similar solution has been implemented to `CategoriesTabContent` in "lib/search/widgets/categories_tab_content.dart".
You can check for its pattern and implement the solution to `CamerasTabContent`.
